import requests
import os

def test_harris_corner_detection():
    """测试Harris角点检测功能"""
    
    # 服务器地址
    url = "http://127.0.0.1:6000/extract/multi_results_base64"
    
    # 测试图像路径（请替换为你的图像路径）
    image_path = "E:\\z30\\DSC_4776.JPG"  # 请确保这个文件存在
    
    if not os.path.exists(image_path):
        print(f"错误：找不到测试图像 {image_path}")
        print("请将一张图像重命名为 test_image.jpg 放在当前目录下")
        return
    
    try:
        print("正在进行Harris角点检测...")
        
        # 准备请求数据
        with open(image_path, 'rb') as f:
            files = {'image': f}
            print("files:",files)
            
            # 发送请求
            response = requests.post(url, files=files)
        
        if response.status_code == 200:
            # 保存结果
            output_filename = "harris_corners_result.jpg"
            with open(output_filename, 'wb') as f:
                f.write(response.content)
            print(f"✓ Harris角点检测成功！结果保存为 {output_filename}")
            print("红色点标记的是检测到的角点")
        else:
            print(f"✗ Harris角点检测失败：{response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"✗ 请求出错：{str(e)}")

if __name__ == "__main__":
    print("=== Harris角点检测测试 ===")
    test_harris_corner_detection()
