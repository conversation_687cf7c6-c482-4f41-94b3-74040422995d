1.运行方式：运行main.py
2.文件说明：api下的五个文件需要放置具体算法，
其中preprocess.py、extract_features.py、filter_features.py
三个文件是按照目前情况对于可见光、红外和SAR的三个处理流程，
在这三个文件中有类似如下：
@preprocess_bp.route('/visible_light', methods=['POST'])
def preprocess_visible_light():
的地方可以加你们的具体算法，至于注释的输入输出参数只是我们的初步模拟，
你们把你们的算法写进去后将其修改后告知我们即可，
值得注意的是，目前写了一个，
但是如果需要按照三种不同类型的图像区分不同的处理算法的话可以增加上面所示的入口，只需不同的路由即可。
剩下的两个分别是本体构建（ontology_build.py）和策略部分（strategy_recognition.py）部分，
与上类似，不过其中目前谈到的处理流程都在一个文件下。