from flask import Flask

from api.ontology_build import ontology_build_bp
from api.preprocess import preprocess_bp
from api.extract_features import extract_features_bp
from api.filter_features import filter_features_bp
from api.strategy_recognition import strategy_recognition_bp
from api.image_analysis import image_analysis_bp

from config import HOST, PORT, DEBU<PERSON>

def create_app():
    app = Flask(__name__)
    # 注册蓝图
    app.register_blueprint(preprocess_bp, url_prefix='/preprocess')
    app.register_blueprint(extract_features_bp, url_prefix='/extract')
    app.register_blueprint(filter_features_bp, url_prefix='/filter')
    app.register_blueprint(ontology_build_bp, url_prefix='/ontologybuild')
    app.register_blueprint(strategy_recognition_bp, url_prefix='/strategy')
    app.register_blueprint(image_analysis_bp, url_prefix='/analysis')
    return app

if __name__ == "__main__":
    app = create_app()
    app.run(host=HOST, port=PORT, debug=DEBUG)

