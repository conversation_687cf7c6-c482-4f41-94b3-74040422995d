from flask import Flask

from app.api.ontology_build import ontology_build_bp
from app.api.preprocess import preprocess_bp
from app.api.extract_features import extract_features_bp
from app.api.filter_features import filter_features_bp
from app.api.strategy_recognition import strategy_recognition_bp

from app.config import HOST, PORT, DEBU<PERSON>

def create_app():
    app = Flask(__name__)
    # 注册蓝图
    app.register_blueprint(preprocess_bp, url_prefix='/preprocess')
    app.register_blueprint(extract_features_bp, url_prefix='/extract')
    app.register_blueprint(filter_features_bp, url_prefix='/filter')
    app.register_blueprint(ontology_build_bp, url_prefix='/ontologybuild')
    app.register_blueprint(strategy_recognition_bp, url_prefix='/strategy')
    return app

if __name__ == "__main__":
    app = create_app()
    app.run(host=HOST, port=PORT, debug=DEBUG)

