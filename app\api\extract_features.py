from flask import Blueprint, request, jsonify, send_file
from io import BytesIO
from PIL import Image
import cv2,os
import numpy as np

extract_features_bp = Blueprint('extract_features', __name__)






@extract_features_bp.route('/visible_light', methods=['POST'])
def extract_features_visible_light():
    """
    可见光特征提取接口

    """
    try:
        # 检查请求中是否包含文件
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        # 获取上传的图像文件
        file = request.files['image']

        # 检查文件是否有文件名
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 读取图像
        img = Image.open(file.stream).convert('RGB')

        #添加实际算法处理!!!

        if img is None:
            print(f"未找到图像: {img}")
            return
        # 去噪
        denoised = cv2.medianBlur(img, 3)
        # 锐化
        kernel = np.array([[0, -1, 0],
                        [-1, 5, -1],
                        [0, -1, 0]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        # 直方图均衡化
        img_yuv = cv2.cvtColor(sharpened, cv2.COLOR_BGR2YUV)
        img_yuv[:, :, 0] = cv2.equalizeHist(img_yuv[:, :, 0])
        equalized = cv2.cvtColor(img_yuv, cv2.COLOR_YUV2BGR)



        # 将处理后的图像保存到内存中
        buffer = BytesIO()
        img.save(buffer, format='JPEG')
        buffer.seek(0)

        # 返回处理后的图像文件
        return send_file(
            buffer,
            mimetype='image/jpeg',
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

#可以按照可见光、红外和SAR分类写三个服务函数

@extract_features_bp.route('/infrared', methods=['POST'])
def extract_features_infrared():
    """
    红外图像特征提取接口
    """
    try:
        # 检查请求中是否包含文件
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        # 获取上传的图像文件
        file = request.files['image']

        # 检查文件是否有文件名
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 读取图像
        img = Image.open(file.stream).convert('RGB')

        # TODO: 添加红外图像特征提取算法
        # 这里可以添加针对红外图像的特殊处理逻辑

        # 将处理后的图像保存到内存中
        buffer = BytesIO()
        img.save(buffer, format='JPEG')
        buffer.seek(0)

        # 返回处理后的图像文件
        return send_file(
            buffer,
            mimetype='image/jpeg',
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500


@extract_features_bp.route('/sar', methods=['POST'])
def extract_features_sar():
    """
    SAR图像特征提取接口
    """
    try:
        # 检查请求中是否包含文件
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        # 获取上传的图像文件
        file = request.files['image']

        # 检查文件是否有文件名
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 读取图像
        img = Image.open(file.stream).convert('RGB')

        # TODO: 添加SAR图像特征提取算法
        # 这里可以添加针对SAR图像的特殊处理逻辑

        # 将处理后的图像保存到内存中
        buffer = BytesIO()
        img.save(buffer, format='JPEG')
        buffer.seek(0)

        # 返回处理后的图像文件
        return send_file(
            buffer,
            mimetype='image/jpeg',
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500