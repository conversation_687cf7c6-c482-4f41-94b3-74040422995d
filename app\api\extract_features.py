from flask import Blueprint, request, jsonify, send_file
from io import BytesIO
from PIL import Image
import cv2,os
import numpy as np
from datetime import datetime
import uuid

extract_features_bp = Blueprint('extract_features', __name__)

@extract_features_bp.route('/visible_light', methods=['POST'])
def extract_features_visible_light():
    """
    可见光特征提取接口

    """
    try:
        # 检查请求中是否包含文件
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        # 获取上传的图像文件
        file = request.files['image']

        # 检查文件是否有文件名
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 读取图像
        img = Image.open(file.stream).convert('RGB')

        # 将PIL图像转换为OpenCV格式（BGR）
        img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

        # 图像锐化处理
        # 方法1：使用拉普拉斯算子锐化
        kernel_laplacian = np.array([[0, -1, 0],
                                   [-1, 5, -1],
                                   [0, -1, 0]])
        sharpened_img = cv2.filter2D(img_cv, -1, kernel_laplacian)

        # 方法2：Unsharp Masking锐化（可选，注释掉了）
        # gaussian_blur = cv2.GaussianBlur(img_cv, (0, 0), 2.0)
        # sharpened_img = cv2.addWeighted(img_cv, 1.5, gaussian_blur, -0.5, 0)

        # 将OpenCV图像转换回PIL格式
        img_processed = Image.fromarray(cv2.cvtColor(sharpened_img, cv2.COLOR_BGR2RGB))


        # 将处理后的图像保存到内存中
        buffer = BytesIO()
        img_processed.save(buffer, format='JPEG')
        buffer.seek(0)

        # 返回处理后的图像文件
        return send_file(
            buffer,
            mimetype='image/jpeg',
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

#可以按照可见光、红外和SAR分类写三个服务函数

@extract_features_bp.route('/sharpen', methods=['POST'])
def sharpen_image():
    """
    图像锐化接口
    接收：图像文件
    返回：锐化后的图像文件
    """
    try:
        # 检查请求中是否包含文件
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        # 获取上传的图像文件
        file = request.files['image']

        # 检查文件是否有文件名
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 读取图像
        img = Image.open(file.stream).convert('RGB')

        # 将PIL图像转换为OpenCV格式（BGR）
        img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

        # 获取锐化强度参数（可选，默认为中等强度）
        intensity = request.form.get('intensity', 'medium')

        if intensity == 'light':
            # 轻度锐化
            kernel = np.array([[0, -0.5, 0],
                             [-0.5, 3, -0.5],
                             [0, -0.5, 0]])
        elif intensity == 'strong':
            # 强度锐化
            kernel = np.array([[0, -1, 0],
                             [-1, 6, -1],
                             [0, -1, 0]])
        else:  # medium
            # 中等锐化（默认）
            kernel = np.array([[0, -1, 0],
                             [-1, 5, -1],
                             [0, -1, 0]])

        # 应用锐化滤波器
        sharpened_img = cv2.filter2D(img_cv, -1, kernel)

        # 将OpenCV图像转换回PIL格式
        img_processed = Image.fromarray(cv2.cvtColor(sharpened_img, cv2.COLOR_BGR2RGB))

        # 将处理后的图像保存到内存中
        buffer = BytesIO()
        img_processed.save(buffer, format='JPEG')
        buffer.seek(0)

        # 返回处理后的图像文件
        return send_file(
            buffer,
            mimetype='image/jpeg',
            as_attachment=False,
            download_name='sharpened_image.jpg'
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@extract_features_bp.route('/infrared', methods=['POST'])
def extract_features_infrared():
    """
    红外图像特征提取接口
    """
    try:
        # 检查请求中是否包含文件
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        # 获取上传的图像文件
        file = request.files['image']

        # 检查文件是否有文件名
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 读取图像
        img = Image.open(file.stream).convert('RGB')

        # TODO: 添加红外图像特征提取算法
        # 这里可以添加针对红外图像的特殊处理逻辑

        # 将处理后的图像保存到内存中
        buffer = BytesIO()
        img.save(buffer, format='JPEG')
        buffer.seek(0)

        # 返回处理后的图像文件
        return send_file(
            buffer,
            mimetype='image/jpeg',
        )

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500




@extract_features_bp.route('/multi_results_base64', methods=['POST'])
@extract_features_bp.route('/multiple', methods=['POST'])
def extract_multiple_features_base64():
    """
    多图像特征提取接口
    将结果保存到服务器文件夹并返回访问信息
    """
    try:
        if 'image' not in request.files:
            return jsonify({
                "status": "error",
                "message": "请求中缺少图像文件"
            }), 400

        file = request.files['image']
        
        if file.filename == '':
            return jsonify({
                "status": "error",
                "message": "未选择图像文件"
            }), 400

        # 文件夹来存储结果
        folder_name = f"1"
        os.makedirs(folder_name, exist_ok=True)

        # 读取图像
        img_pil = Image.open(file.stream).convert('RGB')
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        img_gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

        result_files = []

        # 处理1: 原图锐化
        kernel = np.array([[0, -1, 0], [-1, 5, -1], [0, -1, 0]])
        sharpened_img = cv2.filter2D(img_cv, -1, kernel)
        sharpened_pil = Image.fromarray(cv2.cvtColor(sharpened_img, cv2.COLOR_BGR2RGB))
        
        sharpened_filename = "sharpened_image.jpg"
        sharpened_filepath = os.path.join(folder_name, sharpened_filename)
        sharpened_pil.save(sharpened_filepath)
        result_files.append(sharpened_filename)

        # 处理2: Harris角点检测
        harris_corners = cv2.cornerHarris(img_gray, 2, 3, 0.04)
        harris_img_color = cv2.cvtColor(img_gray, cv2.COLOR_GRAY2BGR)
        harris_img_color[harris_corners > 0.01 * harris_corners.max()] = [0, 0, 255]
        harris_pil = Image.fromarray(cv2.cvtColor(harris_img_color, cv2.COLOR_BGR2RGB))
        
        harris_filename = "harris_corners.jpg"
        harris_filepath = os.path.join(folder_name, harris_filename)
        harris_pil.save(harris_filepath)
        result_files.append(harris_filename)

        # 可以添加更多处理结果...

        return jsonify({
            "status": "success",
            "message": "处理完成",
            "folder": folder_name,
            "files": result_files,
            "folder_path": folder_name
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500